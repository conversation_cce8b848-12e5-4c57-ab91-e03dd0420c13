import 'dart:convert';
import 'dart:async';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../config/api_config.dart';
import '../screens/announcement_screen.dart';

/// 公告服务 - 简化版，无缓存机制，直接实时获取
class AnnouncementService extends GetxService {
  static const String _lastAnnouncementIdKey = 'last_announcement_id';

  final announcement = Rxn<Announcement>();
  final RxBool isLoading = false.obs;

  // 静态公告文件地址
  final String _announcementUrl = '${ApiConfig.baseUrl}/announcement.json';
  final String _backupAnnouncementUrl = '${ApiConfig.backupUrl}/announcement.json';

  // 检查间隔（秒）- 实时推送，每30秒检查一次
  static const int _checkIntervalSeconds = 30;
  
  Timer? _checkTimer;

  @override
  void onInit() {
    super.onInit();
    initAnnouncement();
    // 启动实时检查定时器
    _startRealTimeCheck();
  }

  @override
  void onClose() {
    _checkTimer?.cancel();
    super.onClose();
  }

  /// 初始化公告服务 - 简化版，无缓存
  Future<void> initAnnouncement() async {
    try {
      print('🔔 初始化简化版公告服务（无缓存）...');
      print('🌐 主域名: $_announcementUrl');
      print('🌐 备用域名: $_backupAnnouncementUrl');
      
      // 立即检查一次公告
      await _checkForNewAnnouncementRealTime();
    } catch (e) {
      print('❌ 初始化公告服务失败: $e');
    }
  }

  /// 从静态文件获取最新公告 - 增强调试版本
  Future<Announcement?> _fetchLatestAnnouncement() async {
    print('🌐 开始获取公告...');
    print('📡 主域名URL: $_announcementUrl');
    print('📡 备用域名URL: $_backupAnnouncementUrl');
    
    try {
      final headers = {
        'Accept': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'User-Agent': 'DaizongNovelApp/1.0',
      };

      final client = http.Client();
      try {
        final timeout = Duration(seconds: 10); // 增加超时时间

        // 先尝试主域名
        try {
          print('🔄 尝试主域名: $_announcementUrl');
          final response = await client
              .get(Uri.parse(_announcementUrl), headers: headers)
              .timeout(timeout);

          print('📊 主域名响应状态: ${response.statusCode}');
          print('📄 主域名响应长度: ${response.body.length}');
          print('🔍 主域名响应头: ${response.headers}');
          
          if (response.statusCode == 200 && response.body.isNotEmpty) {
            print('✅ 主域名响应成功，解析JSON...');
            print('📝 响应内容: ${response.body}');
            
            try {
              final data = json.decode(response.body);
              print('🔍 解析后的数据: $data');
              print('🔍 is_active 值: ${data['is_active']}');
              print('🔍 数据类型: ${data['is_active'].runtimeType}');
              
              // 检查公告是否激活
              if (data['is_active'] == true) {
                print('✅ 公告已激活，创建Announcement对象');
                final announcement = Announcement.fromJson(data);
                print('🎯 成功创建公告: ${announcement.title}');
                return announcement;
              } else {
                print('⚠️ 公告未激活 (is_active = ${data['is_active']})');
                return null;
              }
            } catch (jsonError) {
              print('❌ JSON解析失败: $jsonError');
              print('📝 原始响应: ${response.body}');
              return null;
            }
          } else {
            print('❌ 主域名响应失败: 状态码=${response.statusCode}, 内容长度=${response.body.length}');
            if (response.body.isNotEmpty) {
              print('📝 错误响应内容: ${response.body}');
            }
          }
        } catch (e) {
          print('❌ 主域名获取公告失败，尝试备用地址: $e');

          // 如果主域名失败，尝试备用地址
          try {
            print('🔄 尝试备用域名: $_backupAnnouncementUrl');
            final response = await client
                .get(Uri.parse(_backupAnnouncementUrl), headers: headers)
                .timeout(timeout);

            print('📊 备用域名响应状态: ${response.statusCode}');
            print('📄 备用域名响应长度: ${response.body.length}');

            if (response.statusCode == 200 && response.body.isNotEmpty) {
              print('✅ 备用域名响应成功，解析JSON...');
              print('📝 响应内容: ${response.body}');
              
              try {
                final data = json.decode(response.body);
                print('🔍 解析后的数据: $data');
                print('🔍 is_active 值: ${data['is_active']}');
                
                // 检查公告是否激活
                if (data['is_active'] == true) {
                  print('✅ 公告已激活，创建Announcement对象');
                  final announcement = Announcement.fromJson(data);
                  print('🎯 成功创建公告: ${announcement.title}');
                  return announcement;
                } else {
                  print('⚠️ 公告未激活 (is_active = ${data['is_active']})');
                  return null;
                }
              } catch (jsonError) {
                print('❌ 备用域名JSON解析失败: $jsonError');
                return null;
              }
            } else {
              print('❌ 备用域名响应失败: 状态码=${response.statusCode}, 内容长度=${response.body.length}');
            }
          } catch (backupError) {
            print('❌ 备用域名也失败: $backupError');
          }
        }

        print('❌ 所有域名都无法获取有效公告');
        return null;
      } finally {
        client.close();
      }
    } catch (e) {
      print('❌ 获取公告网络请求失败: $e');
      return null;
    }
  }

  /// 手动刷新公告 - 简化版
  Future<void> refreshAnnouncement() async {
    try {
      print('🔄 手动刷新公告...');
      await _checkForNewAnnouncementRealTime();
    } catch (e) {
      print('❌ 手动刷新公告失败: $e');
      Get.snackbar('错误', '刷新公告失败: $e');
    }
  }

  /// 标记公告为已读
  Future<void> markAnnouncementAsRead() async {
    try {
      if (announcement.value != null) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_lastAnnouncementIdKey, announcement.value!.id);
        print('✅ 公告已标记为已读: ${announcement.value!.id}');
      }
    } catch (e) {
      print('❌ 标记公告已读失败: $e');
    }
  }

  /// 显示公告对话框
  void _showAnnouncementDialog(Announcement announcement) {
    try {
      // 确保在UI线程中执行
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (Get.context != null) {
          Get.dialog(
            AnnouncementScreen(announcement: announcement),
            barrierDismissible: false,
          );
          print('📢 公告对话框已显示');
        } else {
          print('⚠️ 无法显示公告对话框：Get.context 为空');
        }
      });
    } catch (e) {
      print('显示公告对话框失败: $e');
    }
  }

  /// 启动实时检查定时器
  void _startRealTimeCheck() {
    print('🔄 启动实时公告检查定时器，每${_checkIntervalSeconds}秒检查一次');
    
    // 使用Timer.periodic创建定时器
    _checkTimer = Timer.periodic(Duration(seconds: _checkIntervalSeconds), (timer) async {
      try {
        print('⏰ 定时检查公告更新...');
        await _checkForNewAnnouncementRealTime();
      } catch (e) {
        print('❌ 定时检查公告失败: $e');
      }
    });
  }

  /// 实时检查新公告 - 简化版，无缓存
  Future<void> _checkForNewAnnouncementRealTime() async {
    try {
      print('🔍 开始实时检查新公告（无缓存模式）...');
      
      // 直接获取最新公告
      final latestAnnouncement = await _fetchLatestAnnouncement();
      
      if (latestAnnouncement != null) {
        print('✅ 获取到公告: ${latestAnnouncement.title} (ID: ${latestAnnouncement.id})');
        
        // 获取上次显示的公告ID
        final prefs = await SharedPreferences.getInstance();
        final lastAnnouncementId = prefs.getString(_lastAnnouncementIdKey);
        
        print('🔍 上次公告ID: $lastAnnouncementId');
        print('🔍 当前公告ID: ${latestAnnouncement.id}');
        
        // 如果是新公告，立即显示
        if (lastAnnouncementId != latestAnnouncement.id) {
          print('🆕 发现新公告: ${latestAnnouncement.title}');
          
          // 更新公告状态
          announcement.value = latestAnnouncement;
          
          // 立即显示公告对话框
          _showAnnouncementDialog(latestAnnouncement);
        } else {
          print('📋 公告无更新 (相同ID)');
        }
      } else {
        print('❌ 服务器无公告或公告获取失败');
      }
    } catch (e) {
      print('❌ 实时检查公告失败: $e');
    }
  }

  /// 清除公告记录（用于调试）
  Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_lastAnnouncementIdKey);
      announcement.value = null;
      print('🗑️ 公告记录已清除');
    } catch (e) {
      print('清除记录失败: $e');
    }
  }
}

/// 公告数据模型
class Announcement {
  final String id;
  final String title;
  final String content;
  final DateTime date;
  final bool isImportant;
  final bool isActive;

  Announcement({
    required this.id,
    required this.title,
    required this.content,
    required this.date,
    this.isImportant = false,
    this.isActive = true,
  });

  /// 从JSON创建Announcement对象
  factory Announcement.fromJson(Map<String, dynamic> json) {
    return Announcement(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      date: _parseDateTime(json['created_at'] as String),
      isImportant: json['is_important'] as bool? ?? false,
      isActive: json['is_active'] as bool? ?? true,
    );
  }

  /// 解析日期时间，处理不标准的ISO格式
  static DateTime _parseDateTime(String dateTimeString) {
    try {
      // 首先尝试直接解析
      return DateTime.parse(dateTimeString);
    } catch (e) {
      // 如果解析失败，尝试修复格式
      // 处理类似 "2025-06-30T9:00:00Z" 的格式，将单位数小时补零
      String fixedDateTime = dateTimeString;

      // 使用正则表达式匹配并修复单位数小时
      final regex = RegExp(r'T(\d):');
      if (regex.hasMatch(fixedDateTime)) {
        fixedDateTime = fixedDateTime.replaceAllMapped(regex, (match) {
          return 'T0${match.group(1)}:';
        });
      }

      try {
        return DateTime.parse(fixedDateTime);
      } catch (e2) {
        // 如果还是失败，返回当前时间
        print('⚠️ 日期解析失败，使用当前时间: $dateTimeString -> $e2');
        return DateTime.now();
      }
    }
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'created_at': date.toIso8601String(),
      'is_important': isImportant,
      'is_active': isActive,
    };
  }
}
